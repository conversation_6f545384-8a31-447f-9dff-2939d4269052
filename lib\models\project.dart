import 'package:uuid/uuid.dart';

class Project {
  final String id;
  final String name;
  final String description;
  final String color; // Hex color code
  final DateTime createdAt;

  Project({
    String? id,
    required this.name,
    required this.description,
    this.color = '#2196F3', // Default blue color
    DateTime? createdAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  Project copyWith({
    String? name,
    String? description,
    String? color,
  }) {
    return Project(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      createdAt: createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Project.fromMap(Map<String, dynamic> map) {
    return Project(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      color: map['color'] ?? '#2196F3',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
    );
  }
}
