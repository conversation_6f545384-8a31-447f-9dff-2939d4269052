import 'package:uuid/uuid.dart';

enum TaskStatus {
  inProgress('In Progress'),
  completed('Completed'),
  postponed('Postponed'),
  cancelled('Cancelled');

  const TaskStatus(this.displayName);
  final String displayName;
}

enum TaskPriority {
  low('Low'),
  medium('Medium'),
  high('High'),
  urgent('Urgent');

  const TaskPriority(this.displayName);
  final String displayName;
}

class TaskAttachment {
  final String id;
  final String name;
  final String path;
  final String type; // 'file', 'image', 'link'
  final DateTime createdAt;

  TaskAttachment({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'type': type,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory TaskAttachment.fromMap(Map<String, dynamic> map) {
    return TaskAttachment(
      id: map['id'],
      name: map['name'],
      path: map['path'],
      type: map['type'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
    );
  }
}

class Task {
  final String id;
  final String title;
  final String description;
  final DateTime createdAt;
  final DateTime? dueDate;
  final TaskStatus status;
  final TaskPriority priority;
  final String? projectId;
  final List<String> categoryIds;
  final List<TaskAttachment> attachments;

  Task({
    String? id,
    required this.title,
    required this.description,
    DateTime? createdAt,
    this.dueDate,
    this.status = TaskStatus.inProgress,
    this.priority = TaskPriority.medium,
    this.projectId,
    this.categoryIds = const [],
    this.attachments = const [],
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  Task copyWith({
    String? title,
    String? description,
    DateTime? dueDate,
    TaskStatus? status,
    TaskPriority? priority,
    String? projectId,
    List<String>? categoryIds,
    List<TaskAttachment>? attachments,
  }) {
    return Task(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt,
      dueDate: dueDate ?? this.dueDate,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      projectId: projectId ?? this.projectId,
      categoryIds: categoryIds ?? this.categoryIds,
      attachments: attachments ?? this.attachments,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'status': status.name,
      'priority': priority.name,
      'projectId': projectId,
      'categoryIds': categoryIds.join(','),
      'attachments': attachments.map((a) => a.toMap()).toList(),
    };
  }

  factory Task.fromMap(Map<String, dynamic> map) {
    return Task(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      dueDate: map['dueDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
          : null,
      status: TaskStatus.values.firstWhere((s) => s.name == map['status']),
      priority: TaskPriority.values.firstWhere((p) => p.name == map['priority']),
      projectId: map['projectId'],
      categoryIds: map['categoryIds'] != null && map['categoryIds'].isNotEmpty
          ? map['categoryIds'].split(',')
          : [],
      attachments: map['attachments'] != null
          ? (map['attachments'] as List).map((a) => TaskAttachment.fromMap(a)).toList()
          : [],
    );
  }
}
