import 'package:flutter/foundation.dart';
import '../models/task.dart';
import '../models/project.dart';
import '../models/category.dart' as models;
import '../services/database_service.dart';

class TaskProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();

  List<Task> _tasks = [];
  List<Project> _projects = [];
  List<models.Category> _categories = [];

  TaskStatus? _statusFilter;
  String? _projectFilter;
  String? _categoryFilter;
  TaskPriority? _priorityFilter;
  String _searchQuery = '';

  List<Task> get tasks {
    List<Task> filteredTasks = List.from(_tasks);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filteredTasks =
          filteredTasks
              .where(
                (task) =>
                    task.title.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ||
                    task.description.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ),
              )
              .toList();
    }

    // Apply status filter
    if (_statusFilter != null) {
      filteredTasks =
          filteredTasks.where((task) => task.status == _statusFilter).toList();
    }

    // Apply project filter
    if (_projectFilter != null) {
      filteredTasks =
          filteredTasks
              .where((task) => task.projectId == _projectFilter)
              .toList();
    }

    // Apply category filter
    if (_categoryFilter != null) {
      filteredTasks =
          filteredTasks
              .where((task) => task.categoryIds.contains(_categoryFilter))
              .toList();
    }

    // Apply priority filter
    if (_priorityFilter != null) {
      filteredTasks =
          filteredTasks
              .where((task) => task.priority == _priorityFilter)
              .toList();
    }

    return filteredTasks;
  }

  List<Project> get projects => _projects;
  List<models.Category> get categories => _categories;

  TaskStatus? get statusFilter => _statusFilter;
  String? get projectFilter => _projectFilter;
  String? get categoryFilter => _categoryFilter;
  TaskPriority? get priorityFilter => _priorityFilter;
  String get searchQuery => _searchQuery;

  Future<void> loadData() async {
    _tasks = await _databaseService.getTasks();
    _projects = await _databaseService.getProjects();
    _categories = await _databaseService.getCategories();
    notifyListeners();
  }

  // Task operations
  Future<void> addTask(Task task) async {
    await _databaseService.insertTask(task);
    _tasks.add(task);
    notifyListeners();
  }

  Future<void> updateTask(Task task) async {
    await _databaseService.updateTask(task);
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      _tasks[index] = task;
      notifyListeners();
    }
  }

  Future<void> deleteTask(String taskId) async {
    await _databaseService.deleteTask(taskId);
    _tasks.removeWhere((task) => task.id == taskId);
    notifyListeners();
  }

  // Project operations
  Future<void> addProject(Project project) async {
    await _databaseService.insertProject(project);
    _projects.add(project);
    notifyListeners();
  }

  Future<void> updateProject(Project project) async {
    await _databaseService.updateProject(project);
    final index = _projects.indexWhere((p) => p.id == project.id);
    if (index != -1) {
      _projects[index] = project;
      notifyListeners();
    }
  }

  Future<void> deleteProject(String projectId) async {
    await _databaseService.deleteProject(projectId);
    _projects.removeWhere((project) => project.id == projectId);
    // Remove project reference from tasks
    for (var task in _tasks.where((t) => t.projectId == projectId)) {
      await updateTask(task.copyWith(projectId: null));
    }
    notifyListeners();
  }

  // Category operations
  Future<void> addCategory(models.Category category) async {
    await _databaseService.insertCategory(category);
    _categories.add(category);
    notifyListeners();
  }

  Future<void> updateCategory(models.Category category) async {
    await _databaseService.updateCategory(category);
    final index = _categories.indexWhere((c) => c.id == category.id);
    if (index != -1) {
      _categories[index] = category;
      notifyListeners();
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    await _databaseService.deleteCategory(categoryId);
    _categories.removeWhere((category) => category.id == categoryId);
    // Remove category reference from tasks
    for (var task in _tasks.where((t) => t.categoryIds.contains(categoryId))) {
      final newCategoryIds = List<String>.from(task.categoryIds);
      newCategoryIds.remove(categoryId);
      await updateTask(task.copyWith(categoryIds: newCategoryIds));
    }
    notifyListeners();
  }

  // Filter operations
  void setStatusFilter(TaskStatus? status) {
    _statusFilter = status;
    notifyListeners();
  }

  void setProjectFilter(String? projectId) {
    _projectFilter = projectId;
    notifyListeners();
  }

  void setCategoryFilter(String? categoryId) {
    _categoryFilter = categoryId;
    notifyListeners();
  }

  void setPriorityFilter(TaskPriority? priority) {
    _priorityFilter = priority;
    notifyListeners();
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void clearFilters() {
    _statusFilter = null;
    _projectFilter = null;
    _categoryFilter = null;
    _priorityFilter = null;
    _searchQuery = '';
    notifyListeners();
  }

  Project? getProjectById(String? projectId) {
    if (projectId == null) return null;
    try {
      return _projects.firstWhere((p) => p.id == projectId);
    } catch (e) {
      return null;
    }
  }

  models.Category? getCategoryById(String categoryId) {
    try {
      return _categories.firstWhere((c) => c.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  List<models.Category> getCategoriesForTask(Task task) {
    return task.categoryIds
        .map((id) => getCategoryById(id))
        .where((category) => category != null)
        .cast<models.Category>()
        .toList();
  }
}
