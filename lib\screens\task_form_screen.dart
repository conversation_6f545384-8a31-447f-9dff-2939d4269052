import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';
import '../models/task.dart';
import '../models/project.dart';
import '../models/category.dart' as models;
import '../providers/task_provider.dart';
import '../widgets/attachment_widget.dart';

class TaskFormScreen extends StatefulWidget {
  final Task? task;

  const TaskFormScreen({super.key, this.task});

  @override
  State<TaskFormScreen> createState() => _TaskFormScreenState();
}

class _TaskFormScreenState extends State<TaskFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  DateTime? _dueDate;
  TaskStatus _status = TaskStatus.inProgress;
  TaskPriority _priority = TaskPriority.medium;
  Project? _selectedProject;
  List<models.Category> _selectedCategories = [];
  List<TaskAttachment> _attachments = [];

  @override
  void initState() {
    super.initState();
    if (widget.task != null) {
      _initializeWithTask(widget.task!);
    }
  }

  void _initializeWithTask(Task task) {
    _titleController.text = task.title;
    _descriptionController.text = task.description;
    _dueDate = task.dueDate;
    _status = task.status;
    _priority = task.priority;
    _attachments = List.from(task.attachments);

    // Set selected project and categories after provider is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final taskProvider = Provider.of<TaskProvider>(context, listen: false);
      _selectedProject = taskProvider.getProjectById(task.projectId);
      _selectedCategories =
          task.categoryIds
              .map((id) => taskProvider.getCategoryById(id))
              .where((category) => category != null)
              .cast<models.Category>()
              .toList();
      setState(() {});
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.task == null ? 'Create Task' : 'Edit Task'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [TextButton(onPressed: _saveTask, child: const Text('Save'))],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Title field
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title *',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Description field
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),

            const SizedBox(height: 16),

            // Due date picker
            InkWell(
              onTap: _selectDueDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Due Date',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _dueDate != null
                      ? DateFormat('MMM dd, yyyy').format(_dueDate!)
                      : 'No due date',
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Priority and Status row
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<TaskPriority>(
                    value: _priority,
                    decoration: const InputDecoration(
                      labelText: 'Priority',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        TaskPriority.values.map((priority) {
                          return DropdownMenuItem(
                            value: priority,
                            child: Text(priority.displayName),
                          );
                        }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _priority = value;
                        });
                      }
                    },
                  ),
                ),

                const SizedBox(width: 16),

                Expanded(
                  child: DropdownButtonFormField<TaskStatus>(
                    value: _status,
                    decoration: const InputDecoration(
                      labelText: 'Status',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        TaskStatus.values.map((status) {
                          return DropdownMenuItem(
                            value: status,
                            child: Text(status.displayName),
                          );
                        }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _status = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Project selector
            Consumer<TaskProvider>(
              builder: (context, taskProvider, child) {
                return DropdownButtonFormField<Project?>(
                  value: _selectedProject,
                  decoration: const InputDecoration(
                    labelText: 'Project',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem<Project?>(
                      value: null,
                      child: Text('No project'),
                    ),
                    ...taskProvider.projects.map((project) {
                      return DropdownMenuItem<Project?>(
                        value: project,
                        child: Text(project.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedProject = value;
                    });
                  },
                );
              },
            ),

            const SizedBox(height: 16),

            // Categories selector
            Consumer<TaskProvider>(
              builder: (context, taskProvider, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Categories',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children:
                          taskProvider.categories.map((category) {
                            final isSelected = _selectedCategories.contains(
                              category,
                            );
                            return FilterChip(
                              label: Text(category.name),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  if (selected) {
                                    _selectedCategories.add(category);
                                  } else {
                                    _selectedCategories.remove(category);
                                  }
                                });
                              },
                            );
                          }).toList(),
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // Attachments section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'Attachments',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    PopupMenuButton<String>(
                      onSelected: _addAttachment,
                      itemBuilder:
                          (context) => [
                            const PopupMenuItem(
                              value: 'file',
                              child: Row(
                                children: [
                                  Icon(Icons.attach_file),
                                  SizedBox(width: 8),
                                  Text('Add File'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'image',
                              child: Row(
                                children: [
                                  Icon(Icons.image),
                                  SizedBox(width: 8),
                                  Text('Add Image'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'link',
                              child: Row(
                                children: [
                                  Icon(Icons.link),
                                  SizedBox(width: 8),
                                  Text('Add Link'),
                                ],
                              ),
                            ),
                          ],
                      child: const Icon(Icons.add),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (_attachments.isEmpty)
                  const Text(
                    'No attachments',
                    style: TextStyle(color: Colors.grey),
                  )
                else
                  ...(_attachments.map(
                    (attachment) => AttachmentWidget(
                      attachment: attachment,
                      onDelete: () => _removeAttachment(attachment),
                    ),
                  )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDueDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (picked != null) {
      setState(() {
        _dueDate = picked;
      });
    }
  }

  void _addAttachment(String type) async {
    try {
      switch (type) {
        case 'file':
          await _addFileAttachment();
          break;
        case 'image':
          await _addImageAttachment();
          break;
        case 'link':
          await _addLinkAttachment();
          break;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error adding attachment: $e')));
      }
    }
  }

  Future<void> _addFileAttachment() async {
    final result = await FilePicker.platform.pickFiles();
    if (result != null && result.files.single.path != null) {
      final file = result.files.single;
      final attachment = TaskAttachment(
        id: const Uuid().v4(),
        name: file.name,
        path: file.path!,
        type: 'file',
        createdAt: DateTime.now(),
      );
      setState(() {
        _attachments.add(attachment);
      });
    }
  }

  Future<void> _addImageAttachment() async {
    final picker = ImagePicker();
    final image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      final attachment = TaskAttachment(
        id: const Uuid().v4(),
        name: image.name,
        path: image.path,
        type: 'image',
        createdAt: DateTime.now(),
      );
      setState(() {
        _attachments.add(attachment);
      });
    }
  }

  Future<void> _addLinkAttachment() async {
    final controller = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add Link'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'URL',
                hintText: 'https://example.com',
              ),
              keyboardType: TextInputType.url,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, controller.text),
                child: const Text('Add'),
              ),
            ],
          ),
    );

    if (result != null && result.trim().isNotEmpty) {
      final uri = Uri.tryParse(result.trim());
      if (uri != null && uri.hasScheme) {
        final attachment = TaskAttachment(
          id: const Uuid().v4(),
          name: uri.host.isNotEmpty ? uri.host : result.trim(),
          path: result.trim(),
          type: 'link',
          createdAt: DateTime.now(),
        );
        setState(() {
          _attachments.add(attachment);
        });
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please enter a valid URL')),
          );
        }
      }
    }
  }

  void _removeAttachment(TaskAttachment attachment) {
    setState(() {
      _attachments.remove(attachment);
    });
  }

  void _saveTask() {
    if (_formKey.currentState!.validate()) {
      final task = Task(
        id: widget.task?.id,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        createdAt: widget.task?.createdAt,
        dueDate: _dueDate,
        status: _status,
        priority: _priority,
        projectId: _selectedProject?.id,
        categoryIds: _selectedCategories.map((c) => c.id).toList(),
        attachments: _attachments,
      );

      final taskProvider = Provider.of<TaskProvider>(context, listen: false);

      if (widget.task == null) {
        taskProvider.addTask(task);
      } else {
        taskProvider.updateTask(task);
      }

      Navigator.pop(context);
    }
  }
}
