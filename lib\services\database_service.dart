import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/task.dart';
import '../models/project.dart';
import '../models/category.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'task_manager.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create projects table
    await db.execute('''
      CREATE TABLE projects(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        color TEXT NOT NULL,
        createdAt INTEGER NOT NULL
      )
    ''');

    // Create categories table
    await db.execute('''
      CREATE TABLE categories(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        color TEXT NOT NULL,
        createdAt INTEGER NOT NULL
      )
    ''');

    // Create tasks table
    await db.execute('''
      CREATE TABLE tasks(
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        dueDate INTEGER,
        status TEXT NOT NULL,
        priority TEXT NOT NULL,
        projectId TEXT,
        categoryIds TEXT,
        attachments TEXT,
        FOREIGN KEY (projectId) REFERENCES projects (id)
      )
    ''');
  }

  // Project CRUD operations
  Future<String> insertProject(Project project) async {
    final db = await database;
    await db.insert('projects', project.toMap());
    return project.id;
  }

  Future<List<Project>> getProjects() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('projects');
    return List.generate(maps.length, (i) => Project.fromMap(maps[i]));
  }

  Future<void> updateProject(Project project) async {
    final db = await database;
    await db.update(
      'projects',
      project.toMap(),
      where: 'id = ?',
      whereArgs: [project.id],
    );
  }

  Future<void> deleteProject(String id) async {
    final db = await database;
    await db.delete(
      'projects',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Category CRUD operations
  Future<String> insertCategory(Category category) async {
    final db = await database;
    await db.insert('categories', category.toMap());
    return category.id;
  }

  Future<List<Category>> getCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('categories');
    return List.generate(maps.length, (i) => Category.fromMap(maps[i]));
  }

  Future<void> updateCategory(Category category) async {
    final db = await database;
    await db.update(
      'categories',
      category.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  Future<void> deleteCategory(String id) async {
    final db = await database;
    await db.delete(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Task CRUD operations
  Future<String> insertTask(Task task) async {
    final db = await database;
    final taskMap = task.toMap();
    taskMap['attachments'] = jsonEncode(taskMap['attachments']);
    await db.insert('tasks', taskMap);
    return task.id;
  }

  Future<List<Task>> getTasks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('tasks');
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      if (map['attachments'] != null && map['attachments'].isNotEmpty) {
        map['attachments'] = jsonDecode(map['attachments']);
      } else {
        map['attachments'] = [];
      }
      return Task.fromMap(map);
    });
  }

  Future<void> updateTask(Task task) async {
    final db = await database;
    final taskMap = task.toMap();
    taskMap['attachments'] = jsonEncode(taskMap['attachments']);
    await db.update(
      'tasks',
      taskMap,
      where: 'id = ?',
      whereArgs: [task.id],
    );
  }

  Future<void> deleteTask(String id) async {
    final db = await database;
    await db.delete(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<Task>> getTasksByProject(String projectId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'projectId = ?',
      whereArgs: [projectId],
    );
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      if (map['attachments'] != null && map['attachments'].isNotEmpty) {
        map['attachments'] = jsonDecode(map['attachments']);
      } else {
        map['attachments'] = [];
      }
      return Task.fromMap(map);
    });
  }

  Future<List<Task>> getTasksByStatus(TaskStatus status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'status = ?',
      whereArgs: [status.name],
    );
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      if (map['attachments'] != null && map['attachments'].isNotEmpty) {
        map['attachments'] = jsonDecode(map['attachments']);
      } else {
        map['attachments'] = [];
      }
      return Task.fromMap(map);
    });
  }
}
