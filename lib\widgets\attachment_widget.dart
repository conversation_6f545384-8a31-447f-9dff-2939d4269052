import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/task.dart';

class AttachmentWidget extends StatelessWidget {
  final TaskAttachment attachment;
  final VoidCallback onDelete;

  const AttachmentWidget({
    super.key,
    required this.attachment,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: _getAttachmentIcon(),
        title: Text(
          attachment.name,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          _getAttachmentSubtitle(),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (attachment.type == 'link')
              IconButton(
                icon: const Icon(Icons.open_in_new),
                onPressed: () => _openAttachment(),
              ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: onDelete,
            ),
          ],
        ),
        onTap: attachment.type != 'link' ? () => _openAttachment() : null,
      ),
    );
  }

  Widget _getAttachmentIcon() {
    switch (attachment.type) {
      case 'image':
        return const Icon(Icons.image, color: Colors.blue);
      case 'link':
        return const Icon(Icons.link, color: Colors.green);
      case 'file':
      default:
        return const Icon(Icons.attach_file, color: Colors.orange);
    }
  }

  String _getAttachmentSubtitle() {
    switch (attachment.type) {
      case 'image':
        return 'Image';
      case 'link':
        return 'Link';
      case 'file':
      default:
        return 'File';
    }
  }

  void _openAttachment() async {
    if (attachment.type == 'link') {
      final uri = Uri.parse(attachment.path);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } else {
      // For files and images, we would typically open them with the system's default app
      // This is a simplified implementation
      final uri = Uri.file(attachment.path);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    }
  }
}
