import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/task.dart';
import '../providers/task_provider.dart';

class FilterBottomSheet extends StatefulWidget {
  const FilterBottomSheet({super.key});

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  TaskStatus? _selectedStatus;
  String? _selectedProject;
  String? _selectedCategory;
  TaskPriority? _selectedPriority;

  @override
  void initState() {
    super.initState();
    final taskProvider = Provider.of<TaskProvider>(context, listen: false);
    _selectedStatus = taskProvider.statusFilter;
    _selectedProject = taskProvider.projectFilter;
    _selectedCategory = taskProvider.categoryFilter;
    _selectedPriority = taskProvider.priorityFilter;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Filter Tasks',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedStatus = null;
                    _selectedProject = null;
                    _selectedCategory = null;
                    _selectedPriority = null;
                  });
                },
                child: const Text('Clear All'),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Status filter
          const Text('Status', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              FilterChip(
                label: const Text('All'),
                selected: _selectedStatus == null,
                onSelected: (selected) {
                  setState(() {
                    _selectedStatus = selected ? null : _selectedStatus;
                  });
                },
              ),
              ...TaskStatus.values.map((status) => FilterChip(
                label: Text(status.displayName),
                selected: _selectedStatus == status,
                onSelected: (selected) {
                  setState(() {
                    _selectedStatus = selected ? status : null;
                  });
                },
              )),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Priority filter
          const Text('Priority', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              FilterChip(
                label: const Text('All'),
                selected: _selectedPriority == null,
                onSelected: (selected) {
                  setState(() {
                    _selectedPriority = selected ? null : _selectedPriority;
                  });
                },
              ),
              ...TaskPriority.values.map((priority) => FilterChip(
                label: Text(priority.displayName),
                selected: _selectedPriority == priority,
                onSelected: (selected) {
                  setState(() {
                    _selectedPriority = selected ? priority : null;
                  });
                },
              )),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Project filter
          Consumer<TaskProvider>(
            builder: (context, taskProvider, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Project', style: TextStyle(fontWeight: FontWeight.w500)),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('All'),
                        selected: _selectedProject == null,
                        onSelected: (selected) {
                          setState(() {
                            _selectedProject = selected ? null : _selectedProject;
                          });
                        },
                      ),
                      ...taskProvider.projects.map((project) => FilterChip(
                        label: Text(project.name),
                        selected: _selectedProject == project.id,
                        onSelected: (selected) {
                          setState(() {
                            _selectedProject = selected ? project.id : null;
                          });
                        },
                      )),
                    ],
                  ),
                ],
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Category filter
          Consumer<TaskProvider>(
            builder: (context, taskProvider, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Category', style: TextStyle(fontWeight: FontWeight.w500)),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('All'),
                        selected: _selectedCategory == null,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = selected ? null : _selectedCategory;
                          });
                        },
                      ),
                      ...taskProvider.categories.map((category) => FilterChip(
                        label: Text(category.name),
                        selected: _selectedCategory == category.id,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = selected ? category.id : null;
                          });
                        },
                      )),
                    ],
                  ),
                ],
              );
            },
          ),
          
          const SizedBox(height: 24),
          
          // Apply button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                final taskProvider = Provider.of<TaskProvider>(context, listen: false);
                taskProvider.setStatusFilter(_selectedStatus);
                taskProvider.setProjectFilter(_selectedProject);
                taskProvider.setCategoryFilter(_selectedCategory);
                taskProvider.setPriorityFilter(_selectedPriority);
                Navigator.pop(context);
              },
              child: const Text('Apply Filters'),
            ),
          ),
          
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
